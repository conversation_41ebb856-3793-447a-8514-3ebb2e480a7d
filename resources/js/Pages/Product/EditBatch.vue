<script setup>
import AppLayout from '@/Layouts/AppLayout.vue';
import { computed, ref, watch } from 'vue';
import { useForm } from '@inertiajs/vue3';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@/components/ui/tabs';
import {
  RadioGroup,
  RadioGroupItem,
} from '@/components/ui/radio-group';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { format } from 'date-fns';

const props = defineProps({
  Product: Object,
  Batch: Object,
});

const activeTab = ref('update');

const initialExpireDate = props.Batch.expireDate
  ? new Date(props.Batch.expireDate).toISOString().split('T')[0]
  : null;

// Update Form
const updateForm = useForm({
  name: props.Product.name,
  salePrice: props.Batch.salesPrice,
  purchasePrice: props.Batch.purchasePrice,
  batchNumber: props.Batch.batchNumber,
  expireDate: initialExpireDate,
  discountRate: props.Batch.discountRate,
});

// Load Form
const loadForm = useForm({
  name: props.Product.name,
  quantity: 0,
  movingUnit: 'sub',
  soldInSubUnit: props.Product.soldInSubUnit,
  salePrice: props.Batch.salesPrice,
  purchasePrice: props.Batch.purchasePrice,
  batchNumber: props.Batch.batchNumber,
  expireDate: initialExpireDate,
  discountRate: props.Batch.discountRate,
});

// Unload Form
const unloadForm = useForm({
  name: props.Product.name,
  quantity: 0,
  description: 'Expired',
  movingUnit: 'sub',
  soldInSubUnit: props.Product.soldInSubUnit,
  batchNumber: props.Batch.batchNumber,
});

const minDate = computed(() => {
  const today = new Date();
  return today.toISOString().split('T')[0];
});

const expireDateInWords = computed(() => {
  return updateForm.expireDate
    ? format(new Date(updateForm.expireDate), 'MMMM d, yyyy')
    : 'No expiry date set';
});

const options = computed(() => [
  { value: 'sub', label: `Sub Units / ${props.Product.quantityUnit}` },
  { value: 'main', label: `Main Units / ${props.Product.packagingUnit}` },
]);

const reasons = [
  { value: 'Expired', label: 'Expired' },
  { value: 'Damaged', label: 'Damaged' },
  { value: 'Lost', label: 'Lost' },
  { value: 'Other', label: 'Other' },
];

// Watch for tab changes to sync expire dates
watch(activeTab, (newTab) => {
  if (newTab === 'load') {
    loadForm.expireDate = updateForm.expireDate;
  }
});

function updateProduct() {
  if (updateForm.expireDate && new Date(updateForm.expireDate) < new Date(minDate.value)) {
    updateForm.errors.expireDate = 'Please select a future date';
    return;
  }

  updateForm.post(route('product.updateBatch', [props.Product.id, props.Batch.id]), {});
}

function loadStock() {
  if (loadForm.expireDate && new Date(loadForm.expireDate) < new Date(minDate.value)) {
    loadForm.errors.expireDate = 'Please select a future date';
    return;
  }

  loadForm.put(route('product.stockInStore', props.Product.id), {});
}

function unloadStock() {
  unloadForm.put(route('product.stockOutStore', props.Product.id), {});
}

if (props.Product.hasStock === 'no') {
  updateForm.expireDate = null;
  loadForm.expireDate = null;
}
</script>

<template>
  <AppLayout title="Manage Batch">
    <div class="py-6 px-4 sm:px-6 lg:px-8">
      <Card class="border border-gray-200 max-w-4xl mx-auto">
        <CardHeader class="bg-gray-50 border-b">
          <CardTitle class="text-xl font-semibold text-gray-800">
            Update <span class="font-bold">{{ Product.name }}</span> - 
            <span class="font-bold text-zata-primary-dark">{{ Batch.batchNumber }}</span> (batch)
          </CardTitle>
        </CardHeader>
        
        <CardContent class="p-6">
          <Tabs v-model="activeTab" default-value="update" class="w-full" v-if="Product.hasStock !== 'no'">
            <TabsList class="grid w-full grid-cols-3 mb-6">
              <TabsTrigger value="update">
                Update
              </TabsTrigger>
              <TabsTrigger value="load">
                Load
              </TabsTrigger>
              <TabsTrigger value="unload">
                Unload
              </TabsTrigger>
            </TabsList>

            <!-- Update Tab -->
            <TabsContent value="update" class="space-y-6">
              <form @submit.prevent="updateProduct" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <Label for="update-salePrice">Sale Price</Label>
                    <Input 
                      id="update-salePrice" 
                      v-model="updateForm.salePrice" 
                      type="number"
                    />
                    <p v-if="updateForm.errors.salePrice" class="text-red-500 text-sm mt-1">{{ updateForm.errors.salePrice }}</p>
                  </div>
                  <div class="space-y-2">
                    <Label for="update-purchasePrice">Purchase Price</Label>
                    <Input 
                      id="update-purchasePrice" 
                      v-model="updateForm.purchasePrice" 
                      type="number"
                    />
                    <p v-if="updateForm.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{ updateForm.errors.purchasePrice }}</p>
                  </div>
                </div>

                <div class="space-y-2">
                  <Label for="update-discountRate">Discount Rate</Label>
                  <Input 
                    id="update-discountRate" 
                    v-model="updateForm.discountRate" 
                    type="number"
                  />
                  <p v-if="updateForm.errors.discountRate" class="text-red-500 text-sm mt-1">{{ updateForm.errors.discountRate }}</p>
                </div>

                <div class="space-y-2">
                  <Label for="update-batchNumber">Batch Number</Label>
                  <Input 
                    id="update-batchNumber" 
                    v-model="updateForm.batchNumber" 
                    type="text" 
                    class="bg-gray-200" 
                    disabled 
                  />
                </div>

                <div class="space-y-2" v-if="Product.hasStock === 'yes'">
                  <Label for="update-expireDate">Expiry Date</Label>
                  <Input 
                    id="update-expireDate" 
                    v-model="updateForm.expireDate" 
                    type="date" 
                    :min="minDate"
                  />
                  <p v-if="updateForm.errors.expireDate" class="text-red-500 text-sm mt-1">{{ updateForm.errors.expireDate }}</p>
                </div>

                <div class="flex justify-end gap-4">
                  <Button 
                    type="submit" 
                    :disabled="updateForm.processing"
                    :class="{ 'opacity-25': updateForm.processing }"
                  >
                    Update
                  </Button>
                </div>
              </form>

              <div class="mt-4 text-gray-600 text-sm" v-if="Product.hasStock === 'yes'">
                <p>Expiry Date: <span class="font-medium">{{ expireDateInWords }}</span></p>
              </div>
            </TabsContent>

            <!-- Load Tab -->
            <TabsContent value="load" class="space-y-6">
              <form @submit.prevent="loadStock" class="space-y-6">
                <div class="space-y-2">
                  <Label for="load-quantity">Quantity</Label>
                  <Input 
                    id="load-quantity" 
                    v-model="loadForm.quantity" 
                    type="number"
                  />
                  <p v-if="loadForm.errors.quantity" class="text-red-500 text-sm mt-1">{{ loadForm.errors.quantity }}</p>
                </div>

                <div v-if="loadForm.soldInSubUnit" class="space-y-2">
                  <Label>Load Unit</Label>
                  <RadioGroup v-model="loadForm.movingUnit" class="flex space-x-4">
                    <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                      <RadioGroupItem :value="option.value" :id="`load-unit-${option.value}`" />
                      <Label :for="`load-unit-${option.value}`">{{ option.label }}</Label>
                    </div>
                  </RadioGroup>
                  <p v-if="loadForm.errors.movingUnit" class="text-red-500 text-sm mt-1">{{ loadForm.errors.movingUnit }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div class="space-y-2">
                    <Label for="load-salePrice">Sale Price</Label>
                    <Input 
                      id="load-salePrice" 
                      v-model="loadForm.salePrice" 
                      type="number"
                    />
                    <p v-if="loadForm.errors.salePrice" class="text-red-500 text-sm mt-1">{{ loadForm.errors.salePrice }}</p>
                  </div>
                  <div class="space-y-2">
                    <Label for="load-purchasePrice">Purchase Price</Label>
                    <Input 
                      id="load-purchasePrice" 
                      v-model="loadForm.purchasePrice" 
                      type="number"
                    />
                    <p v-if="loadForm.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{ loadForm.errors.purchasePrice }}</p>
                  </div>
                </div>

                <div class="space-y-2">
                  <Label for="load-discountRate">Discount Rate</Label>
                  <Input 
                    id="load-discountRate" 
                    v-model="loadForm.discountRate" 
                    type="number"
                  />
                  <p v-if="loadForm.errors.discountRate" class="text-red-500 text-sm mt-1">{{ loadForm.errors.discountRate }}</p>
                </div>

                <div class="space-y-2">
                  <Label for="load-batchNumber">Batch Number</Label>
                  <Input 
                    id="load-batchNumber" 
                    v-model="loadForm.batchNumber" 
                    type="text" 
                    class="bg-gray-200" 
                    disabled 
                  />
                </div>

                <div class="space-y-2">
                  <Label for="load-expireDate">Expiry Date</Label>
                  <Input 
                    id="load-expireDate" 
                    v-model="loadForm.expireDate" 
                    type="date" 
                    :min="minDate"
                  />
                  <p v-if="loadForm.errors.expireDate" class="text-red-500 text-sm mt-1">{{ loadForm.errors.expireDate }}</p>
                </div>

                <div class="flex justify-end gap-4">
                  <Button 
                    type="submit" 
                    :disabled="loadForm.processing"
                    :class="{ 'opacity-25': loadForm.processing }"
                  >
                    Load Stock
                  </Button>
                </div>

                <p v-if="loadForm.recentlySuccessful" class="text-green-600 text-sm">
                  Successfully updated.
                </p>
              </form>
            </TabsContent>

            <!-- Unload Tab -->
            <TabsContent value="unload" class="space-y-6">
              <form @submit.prevent="unloadStock" class="space-y-6">
                <div class="space-y-2">
                  <Label for="unload-quantity">Quantity</Label>
                  <Input 
                    id="unload-quantity" 
                    v-model="unloadForm.quantity" 
                    type="number"
                    :max="Batch.currentStock"
                  />
                  <p v-if="unloadForm.errors.quantity" class="text-red-500 text-sm mt-1">{{ unloadForm.errors.quantity }}</p>
                </div>

                <div v-if="unloadForm.soldInSubUnit" class="space-y-2">
                  <Label>Load Unit</Label>
                  <RadioGroup v-model="unloadForm.movingUnit" class="flex space-x-4">
                    <div v-for="option in options" :key="option.value" class="flex items-center space-x-2">
                      <RadioGroupItem :value="option.value" :id="`unload-unit-${option.value}`" />
                      <Label :for="`unload-unit-${option.value}`">{{ option.label }}</Label>
                    </div>
                  </RadioGroup>
                  <p v-if="unloadForm.errors.movingUnit" class="text-red-500 text-sm mt-1">{{ unloadForm.errors.movingUnit }}</p>
                </div>

                <div class="space-y-2">
                  <Label>Reason</Label>
                  <RadioGroup v-model="unloadForm.description" class="flex flex-wrap gap-4">
                    <div v-for="reason in reasons" :key="reason.value" class="flex items-center space-x-2">
                      <RadioGroupItem :value="reason.value" :id="`unload-reason-${reason.value}`" />
                      <Label :for="`unload-reason-${reason.value}`">{{ reason.label }}</Label>
                    </div>
                  </RadioGroup>
                  <p v-if="unloadForm.errors.description" class="text-red-500 text-sm mt-1">{{ unloadForm.errors.description }}</p>
                </div>

                <div class="flex justify-end gap-4">
                  <Button 
                    type="submit" 
                    :disabled="unloadForm.processing"
                    :class="{ 'opacity-25': unloadForm.processing }"
                    class="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Stock Out
                  </Button>
                </div>

                <p v-if="unloadForm.recentlySuccessful" class="text-green-600 text-sm">
                  Successfully updated.
                </p>
              </form>
            </TabsContent>
          </Tabs>

          <!-- No Stock Product - Only Update Tab -->
          <div v-else class="space-y-6">
            <form @submit.prevent="updateProduct" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-2">
                  <Label for="update-salePrice">Sale Price</Label>
                  <Input 
                    id="update-salePrice" 
                    v-model="updateForm.salePrice" 
                    type="number"
                  />
                  <p v-if="updateForm.errors.salePrice" class="text-red-500 text-sm mt-1">{{ updateForm.errors.salePrice }}</p>
                </div>
                <div class="space-y-2">
                  <Label for="update-purchasePrice">Purchase Price</Label>
                  <Input 
                    id="update-purchasePrice" 
                    v-model="updateForm.purchasePrice" 
                    type="number"
                  />
                  <p v-if="updateForm.errors.purchasePrice" class="text-red-500 text-sm mt-1">{{ updateForm.errors.purchasePrice }}</p>
                </div>
              </div>

              <div class="space-y-2">
                <Label for="update-discountRate">Discount Rate</Label>
                <Input 
                  id="update-discountRate" 
                  v-model="updateForm.discountRate" 
                  type="number"
                />
                <p v-if="updateForm.errors.discountRate" class="text-red-500 text-sm mt-1">{{ updateForm.errors.discountRate }}</p>
              </div>

              <div class="flex justify-end gap-4">
                <Button 
                  type="submit" 
                  :disabled="updateForm.processing"
                  :class="{ 'opacity-25': updateForm.processing }"
                >
                  Update
                </Button>
              </div>
            </form>
          </div>
        </CardContent>
      </Card>

      <!-- Current Stock Display -->
      <div class="flex justify-center mt-6">
        <Card class="">
          <CardContent class="p-4">
            <p class="text-center text-gray-700 text-lg">
              Current Batch Stock: <span class="font-bold text-zata-primary-dark">{{ Batch.currentStock }}</span> {{ Product.quantityUnit }}
            </p>
            <p class="text-center text-gray-600 text-sm mt-1">
              Expire Date: <span class="font-medium">{{ expireDateInWords }}</span>
            </p>
          </CardContent>
        </Card>
      </div>
    </div>

    <template #sidenav />
    <template #footer />
  </AppLayout>
</template>